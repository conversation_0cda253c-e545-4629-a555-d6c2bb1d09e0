import { useCallback } from "react"
import { UseFormReturn } from "react-hook-form"
import { InvoiceFormData } from "@/lib/validations"

interface UseInvoiceCalculationsProps {
  form: UseFormReturn<InvoiceFormData>
}

export function useInvoiceCalculations({ form }: UseInvoiceCalculationsProps) {
  const calculateTotals = useCallback(() => {
    const items = form.getValues("items")
    if (!items || items.length === 0) return

    let subtotal = 0

    // Update individual item amounts and calculate subtotal
    items.forEach((item, index) => {
      const quantity = Number(item.quantity) || 0
      const rate = Number(item.rate) || 0
      const amount = quantity * rate

      // Update the individual item amount if it's different
      const currentAmount = form.getValues(`items.${index}.amount`)
      if (currentAmount !== amount) {
        form.setValue(`items.${index}.amount`, amount, { shouldValidate: false })
      }
      subtotal += amount
    })

    const taxAmount = Number(form.getValues("tax_amount")) || 0
    const total = subtotal + taxAmount

    // Update form totals if they're different
    const currentSubtotal = form.getValues("amount")
    const currentTotal = form.getValues("total_amount")

    if (currentSubtotal !== subtotal) {
      form.setValue("amount", subtotal, { shouldValidate: false })
    }
    if (currentTotal !== total) {
      form.setValue("total_amount", total, { shouldValidate: false })
    }
  }, [form])

  const handleItemChange = useCallback((callback: () => void) => {
    callback()
    // Trigger calculation immediately
    setTimeout(() => calculateTotals(), 0)
  }, [calculateTotals])

  return {
    calculateTotals,
    handleItemChange,
  }
}
