import { useState, useEffect, useCallback } from "react"
import { getAvailableClients } from "@/lib/api/projects-client"
import { getProjectsByClient } from "@/lib/api/invoices-client"

interface Client {
  id: string
  name: string
  company?: string
}

interface Project {
  id: string
  name: string
  status: string
}

export function useInvoiceData() {
  const [clients, setClients] = useState<Client[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loadingData, setLoadingData] = useState(true)

  // Load clients on mount
  useEffect(() => {
    const loadClients = async () => {
      try {
        const clientsData = await getAvailableClients()
        setClients(clientsData)
      } catch (error) {
        console.error("Failed to load clients:", error)
        setClients([])
      } finally {
        setLoadingData(false)
      }
    }

    loadClients()
  }, [])

  // Load projects when client is selected
  const loadProjectsForClient = useCallback(async (clientId: string) => {
    if (!clientId || clientId === "") {
      setProjects([])
      return
    }

    try {
      const projectsData = await getProjectsByClient(clientId)
      setProjects(projectsData)
    } catch (error) {
      console.error("Failed to load projects:", error)
      setProjects([])
    }
  }, [])

  return {
    clients,
    projects,
    loadingData,
    loadProjectsForClient,
  }
}
