import { getProjectStatusData } from "@/lib/api/dashboard"
import { ProjectStatusChart } from "./project-status-chart"

export async function ProjectStatusChartServer() {
  try {
    const data = await getProjectStatusData()
    return <ProjectStatusChart data={data} />
  } catch (error) {
    console.error("Failed to fetch project status data:", error)
    // Return fallback with empty data
    return <ProjectStatusChart data={[]} />
  }
}
