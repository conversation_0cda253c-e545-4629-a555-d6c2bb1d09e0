import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { DollarSign, Users, FolderOpen, FileText, TrendingUp, Target } from "lucide-react"
import { DashboardMetrics as DashboardMetricsType } from "@/lib/types"

interface DashboardMetricsProps {
  metrics: DashboardMetricsType
}

export function DashboardMetrics({ metrics }: DashboardMetricsProps) {
  if (!metrics) {
    return <div>Loading metrics...</div>
  }

  const metricCards = [
    {
      title: "Total Clients",
      value: metrics.totalClients || 0,
      icon: Users,
      description: "Active clients",
    },
    {
      title: "Active Projects",
      value: metrics.activeProjects || 0,
      icon: FolderOpen,
      description: "In progress",
    },
    {
      title: "Pending Invoices",
      value: metrics.pendingInvoices || 0,
      icon: FileText,
      description: "Awaiting payment",
    },
    {
      title: "Total Revenue",
      value: `$${(metrics.totalRevenue || 0).toLocaleString()}`,
      icon: DollarSign,
      description: "All time",
    },
    {
      title: "Monthly Revenue",
      value: `$${(metrics.monthlyRevenue || 0).toLocaleString()}`,
      icon: TrendingUp,
      description: "This month",
    },
    {
      title: "Conversion Rate",
      value: `${metrics.conversionRate || 0}%`,
      icon: Target,
      description: "Lead to client",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {metricCards.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <p className="text-xs text-muted-foreground">
              {metric.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
