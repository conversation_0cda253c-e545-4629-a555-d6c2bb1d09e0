import { getRevenueChartData } from "@/lib/api/dashboard"
import { RevenueChart } from "./revenue-chart"

export async function RevenueChartServer() {
  try {
    const data = await getRevenueChartData()
    return <RevenueChart data={data} />
  } catch (error) {
    console.error("Failed to fetch revenue chart data:", error)
    // Return fallback with empty data
    return <RevenueChart data={[]} />
  }
}
