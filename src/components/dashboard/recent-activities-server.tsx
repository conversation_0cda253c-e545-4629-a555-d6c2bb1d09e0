import { getRecentActivities } from "@/lib/api/dashboard"
import { RecentActivities } from "./recent-activities"

export async function RecentActivitiesServer() {
  try {
    const activities = await getRecentActivities()
    return <RecentActivities activities={activities} />
  } catch (error) {
    console.error("Failed to fetch recent activities:", error)
    // Return fallback with empty activities
    return <RecentActivities activities={[]} />
  }
}
