"use client"

import { Control } from "react-hook-form"
import { Textarea } from "@/components/ui/textarea"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { InvoiceFormData } from "@/lib/validations"

interface InvoiceNotesSectionProps {
  control: Control<InvoiceFormData>
}

export function InvoiceNotesSection({ control }: InvoiceNotesSectionProps) {
  return (
    <FormField
      control={control}
      name="notes"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Notes</FormLabel>
          <FormControl>
            <Textarea
              placeholder="Additional notes or payment terms"
              rows={3}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
