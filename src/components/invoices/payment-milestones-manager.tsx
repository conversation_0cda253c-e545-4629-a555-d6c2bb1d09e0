"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Calendar,
  CheckCircle,
  DollarSign,
  MoreHorizontal,
  Plus,
  AlertTriangle,
  Eye,
  Trash2
} from "lucide-react"
import { formatCurrency } from "@/lib/utils/currency"
import { formatDistanceToNow, format } from "date-fns"
import {
  getProjectMilestonesSummary,
  updateMilestoneStatus,
  deleteMilestone,
  type ProjectMilestonesSummary
} from "@/lib/api/milestones-client"
import { MilestoneCreationForm } from "./milestone-creation-form"
import Link from "next/link"

interface PaymentMilestonesManagerProps {
  projectId: string
  projectName: string
  projectBudget: number | null
  onMilestoneUpdate?: () => void
}

const statusColors = {
  draft: "bg-gray-100 text-gray-800",
  sent: "bg-blue-100 text-blue-800",
  paid: "bg-green-100 text-green-800",
  overdue: "bg-red-100 text-red-800",
  cancelled: "bg-gray-100 text-gray-600"
}

const milestoneTypeLabels = {
  dp: "Down Payment",
  progress: "Progress Payment",
  final: "Final Payment",
  standard: "Standard Invoice"
}

export function PaymentMilestonesManager({
  projectId,
  projectName,
  projectBudget,
  onMilestoneUpdate
}: PaymentMilestonesManagerProps) {
  const [summary, setSummary] = useState<ProjectMilestonesSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null)

  const fetchMilestones = async () => {
    setLoading(true)
    try {
      const data = await getProjectMilestonesSummary(projectId)
      setSummary(data)
    } catch (error) {
      console.error("Failed to fetch milestones:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMilestones()
  }, [projectId])

  const handleStatusUpdate = async (invoiceId: string, status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled') => {
    setUpdatingStatus(invoiceId)
    try {
      const paidDate = status === 'paid' ? new Date().toISOString().split('T')[0] : undefined
      await updateMilestoneStatus(invoiceId, status, paidDate)
      await fetchMilestones()
      onMilestoneUpdate?.()
    } catch (error) {
      console.error("Failed to update milestone status:", error)
    } finally {
      setUpdatingStatus(null)
    }
  }

  const handleDeleteMilestone = async (invoiceId: string) => {
    if (!confirm("Are you sure you want to delete this milestone? This action cannot be undone.")) {
      return
    }
    
    try {
      await deleteMilestone(invoiceId)
      await fetchMilestones()
      onMilestoneUpdate?.()
    } catch (error) {
      console.error("Failed to delete milestone:", error)
    }
  }

  const handleCreateSuccess = () => {
    fetchMilestones()
    onMilestoneUpdate?.()
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading milestones...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!projectBudget) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Budget Set</h3>
            <p className="text-muted-foreground mb-4">
              This project needs a budget to create payment milestones.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const hasMilestones = summary && summary.milestones.length > 0

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      {hasMilestones && summary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Payment Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Project Budget</p>
                <p className="text-xl font-bold">{formatCurrency(summary.project_budget || 0, "IDR")}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Total Invoiced</p>
                <p className="text-xl font-bold text-blue-600">{formatCurrency(summary.total_invoiced, "IDR")}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Remaining Budget</p>
                <p className="text-xl font-bold text-green-600">{formatCurrency(summary.remaining_budget, "IDR")}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Completion</p>
                <p className="text-xl font-bold">{summary.completion_percentage}%</p>
              </div>
            </div>
            <Progress value={summary.completion_percentage} className="h-2" />
          </CardContent>
        </Card>
      )}

      {/* Milestones List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Payment Milestones</CardTitle>
            {!hasMilestones && (
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Milestones
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {!hasMilestones ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Payment Milestones</h3>
              <p className="text-muted-foreground mb-4">
                Create payment milestones to track project payments and progress.
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Milestones
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {summary!.milestones.map((milestone) => (
                <div key={milestone.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">
                        {milestoneTypeLabels[milestone.milestone_type]}
                      </Badge>
                      <Badge className={statusColors[milestone.status]}>
                        {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}
                      </Badge>
                      <span className="font-medium">{milestone.invoice_number}</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/invoices/${milestone.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Invoice
                          </Link>
                        </DropdownMenuItem>
                        {milestone.status === 'draft' && (
                          <DropdownMenuItem
                            onClick={() => handleStatusUpdate(milestone.id, 'sent')}
                            disabled={updatingStatus === milestone.id}
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark as Sent
                          </DropdownMenuItem>
                        )}
                        {milestone.status === 'sent' && (
                          <DropdownMenuItem
                            onClick={() => handleStatusUpdate(milestone.id, 'paid')}
                            disabled={updatingStatus === milestone.id}
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark as Paid
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDeleteMilestone(milestone.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Amount</p>
                      <p className="font-semibold">{formatCurrency(milestone.total_amount, "IDR")}</p>
                      <p className="text-xs text-muted-foreground">{milestone.milestone_percentage}% of budget</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Due Date</p>
                      <p className="font-semibold">
                        {milestone.due_date ? format(new Date(milestone.due_date), 'MMM dd, yyyy') : 'Not set'}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">
                        {milestone.status === 'paid' ? 'Paid Date' : 'Created'}
                      </p>
                      <p className="font-semibold">
                        {milestone.status === 'paid' && milestone.paid_date
                          ? format(new Date(milestone.paid_date), 'MMM dd, yyyy')
                          : formatDistanceToNow(new Date(milestone.created_at), { addSuffix: true })
                        }
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Milestones Form */}
      {projectBudget && (
        <MilestoneCreationForm
          open={showCreateForm}
          onOpenChange={setShowCreateForm}
          projectId={projectId}
          projectName={projectName}
          projectBudget={projectBudget}
          onSuccess={handleCreateSuccess}
        />
      )}
    </div>
  )
}
