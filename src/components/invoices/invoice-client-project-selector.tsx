"use client"

import { useEffect } from "react"
import { Control } from "react-hook-form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { InvoiceFormData } from "@/lib/validations"
import { getAvailableCurrencies } from "@/lib/utils/currency"

interface Client {
  id: string
  name: string
  company?: string
}

interface Project {
  id: string
  name: string
  status: string
}

interface InvoiceClientProjectSelectorProps {
  control: Control<InvoiceFormData>
  clients: Client[]
  projects: Project[]
  selectedClientId: string
  onClientChange: (clientId: string) => void
  onProjectReset: () => void
  loadProjectsForClient: (clientId: string) => void
}

export function InvoiceClientProjectSelector({
  control,
  clients,
  projects,
  selectedClientId,
  onClientChange,
  onProjectReset,
  loadProjectsForClient,
}: InvoiceClientProjectSelectorProps) {
  const availableCurrencies = getAvailableCurrencies()

  // Load projects when selected client changes
  useEffect(() => {
    if (selectedClientId) {
      loadProjectsForClient(selectedClientId)
    }
  }, [selectedClientId, loadProjectsForClient])

  return (
    <div className="grid grid-cols-3 gap-4">
      <FormField
        control={control}
        name="client_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Client *</FormLabel>
            <Select 
              onValueChange={(value) => {
                field.onChange(value)
                onClientChange(value)
                onProjectReset()
              }} 
              value={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select client" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {clients.map((client) => (
                  <SelectItem key={client.id} value={client.id}>
                    {client.name}
                    {client.company && ` (${client.company})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="project_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Project</FormLabel>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select project (optional)" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="none">No Project</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="currency"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Currency</FormLabel>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {availableCurrencies.map((currency) => (
                  <SelectItem key={currency.code} value={currency.code}>
                    {currency.code} - {currency.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
