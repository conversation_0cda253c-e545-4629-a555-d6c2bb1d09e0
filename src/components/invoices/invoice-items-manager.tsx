"use client"

import { Control, FieldArrayWithId } from "react-hook-form"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { InvoiceFormData } from "@/lib/validations"
import { Plus, Trash2 } from "lucide-react"

interface InvoiceItemsManagerProps {
  control: Control<InvoiceFormData>
  fields: FieldArrayWithId<InvoiceFormData, "items", "id">[]
  onAddItem: () => void
  onRemoveItem: (index: number) => void
  onItemChange: (callback: () => void) => void
}

export function InvoiceItemsManager({
  control,
  fields,
  onAddItem,
  onRemoveItem,
  onItemChange,
}: InvoiceItemsManagerProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Invoice Items</CardTitle>
          <Button type="button" variant="outline" size="sm" onClick={onAddItem}>
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {fields.map((field, index) => (
          <div key={field.id} className="grid grid-cols-12 gap-2 items-end">
            <div className="col-span-5">
              <FormField
                control={control}
                name={`items.${index}.description`}
                render={({ field }) => (
                  <FormItem>
                    {index === 0 && <FormLabel>Description</FormLabel>}
                    <FormControl>
                      <Input
                        placeholder="Item description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="col-span-2">
              <FormField
                control={control}
                name={`items.${index}.quantity`}
                render={({ field }) => (
                  <FormItem>
                    {index === 0 && <FormLabel>Quantity</FormLabel>}
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => onItemChange(() => {
                          field.onChange(Number(e.target.value))
                        })}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="col-span-2">
              <FormField
                control={control}
                name={`items.${index}.rate`}
                render={({ field }) => (
                  <FormItem>
                    {index === 0 && <FormLabel>Rate</FormLabel>}
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => onItemChange(() => {
                          field.onChange(Number(e.target.value))
                        })}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="col-span-2">
              <FormField
                control={control}
                name={`items.${index}.amount`}
                render={({ field }) => (
                  <FormItem>
                    {index === 0 && <FormLabel>Amount</FormLabel>}
                    <FormControl>
                      <Input
                        type="number"
                        readOnly
                        value={typeof field.value === 'number' ? field.value.toFixed(2) : '0.00'}
                        className="bg-muted"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="col-span-1">
              {index === 0 && <div className="h-6"></div>}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onRemoveItem(index)}
                disabled={fields.length === 1}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
