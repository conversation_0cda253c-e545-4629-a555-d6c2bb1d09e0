"use client"

import React, { useState } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Plus, Trash2, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createMilestonesSchema, type CreateMilestonesData } from "@/lib/validations"
import { createProjectMilestones } from "@/lib/api/milestones-client"
import { formatCurrency } from "@/lib/utils/currency"

interface MilestoneCreationFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  projectId: string
  projectName: string
  projectBudget: number
  onSuccess: () => void
}

const milestoneTypeLabels = {
  dp: "Down Payment (DP)",
  progress: "Progress Payment",
  final: "Final Payment"
}

export function MilestoneCreationForm({
  open,
  onOpenChange,
  projectId,
  projectName,
  projectBudget,
  onSuccess
}: MilestoneCreationFormProps) {
  const [loading, setLoading] = useState(false)

  const form = useForm<CreateMilestonesData>({
    resolver: zodResolver(createMilestonesSchema),
    defaultValues: {
      project_id: projectId,
      milestones: [
        {
          type: "dp",
          percentage: 50,
          description: "Down Payment - Project Initiation",
          due_date: ""
        },
        {
          type: "final",
          percentage: 50,
          description: "Final Payment - Project Completion",
          due_date: ""
        }
      ]
    }
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "milestones"
  })

  const watchedMilestones = form.watch("milestones")
  const totalPercentage = watchedMilestones.reduce((sum, milestone) => sum + (milestone.percentage || 0), 0)
  const isValidTotal = totalPercentage === 100

  const onSubmit = async (data: CreateMilestonesData) => {
    setLoading(true)
    try {
      await createProjectMilestones(data)
      onSuccess()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error("Failed to create milestones:", error)
      // You might want to show a toast notification here
    } finally {
      setLoading(false)
    }
  }

  const addMilestone = () => {
    append({
      type: "progress",
      percentage: 0,
      description: "",
      due_date: ""
    })
  }

  const getAvailableTypes = (currentIndex: number) => {
    const usedTypes = watchedMilestones
      .map((milestone, index) => index !== currentIndex ? milestone.type : null)
      .filter(Boolean)
    
    return [
      { value: "dp", label: milestoneTypeLabels.dp, disabled: usedTypes.includes("dp") },
      { value: "progress", label: milestoneTypeLabels.progress, disabled: false },
      { value: "final", label: milestoneTypeLabels.final, disabled: usedTypes.includes("final") }
    ]
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Payment Milestones</DialogTitle>
          <DialogDescription>
            Set up payment milestones for <strong>{projectName}</strong> with budget of {formatCurrency(projectBudget, "IDR")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Summary Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Milestone Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Milestones</p>
                    <p className="text-2xl font-bold">{fields.length}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Percentage</p>
                    <p className={`text-2xl font-bold ${isValidTotal ? 'text-green-600' : 'text-red-600'}`}>
                      {totalPercentage}%
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Project Budget</p>
                    <p className="text-2xl font-bold">{formatCurrency(projectBudget, "IDR")}</p>
                  </div>
                </div>
                
                {!isValidTotal && (
                  <Alert className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Total percentage must equal 100%. Current total: {totalPercentage}%
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Milestones */}
            <div className="space-y-4">
              {fields.map((field, index) => {
                const milestone = watchedMilestones[index]
                const amount = milestone ? Math.round(projectBudget * milestone.percentage / 100) : 0
                
                return (
                  <Card key={field.id}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          Milestone {index + 1}
                          {milestone?.type && (
                            <Badge variant="outline" className="ml-2">
                              {milestoneTypeLabels[milestone.type as keyof typeof milestoneTypeLabels]}
                            </Badge>
                          )}
                        </CardTitle>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => remove(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`milestones.${index}.type`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Milestone Type</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {getAvailableTypes(index).map((type) => (
                                    <SelectItem 
                                      key={type.value} 
                                      value={type.value}
                                      disabled={type.disabled}
                                    >
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`milestones.${index}.percentage`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Percentage (%)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="100"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-sm text-muted-foreground">
                                Amount: {formatCurrency(amount, "IDR")}
                              </p>
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name={`milestones.${index}.description`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe this milestone payment..."
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`milestones.${index}.due_date`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Due Date (Optional)</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Add Milestone Button */}
            <Button
              type="button"
              variant="outline"
              onClick={addMilestone}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Progress Milestone
            </Button>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || !isValidTotal}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Milestones
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
