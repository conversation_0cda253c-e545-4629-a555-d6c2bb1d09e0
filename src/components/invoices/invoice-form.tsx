"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form } from "@/components/ui/form"
import { Invoice } from "@/lib/types"

import { Loader2 } from "lucide-react"
import { useInvoiceForm } from "@/hooks/useInvoiceForm"
import { useInvoiceData } from "@/hooks/useInvoiceData"
import { useInvoiceCalculations } from "@/hooks/useInvoiceCalculations"
import { InvoiceClientProjectSelector } from "./invoice-client-project-selector"
import { InvoiceItemsManager } from "./invoice-items-manager"
import { InvoiceTotalsSection } from "./invoice-totals-section"
import { InvoiceDatesAndStatus } from "./invoice-dates-and-status"
import { InvoiceNotesSection } from "./invoice-notes-section"

interface InvoiceFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice?: Invoice
  onSuccess: () => void
  preselectedClientId?: string
  preselectedProjectId?: string
}

export function InvoiceForm({ 
  open, 
  onOpenChange, 
  invoice, 
  onSuccess, 
  preselectedClientId,
  preselectedProjectId 
}: InvoiceFormProps) {
  const [selectedClientId, setSelectedClientId] = useState<string>("")
  
  // Use custom hooks
  const { clients, projects, loadingData, loadProjectsForClient } = useInvoiceData()
  const { form, fields, loading, isEditing, onSubmit, addItem, removeItem } = useInvoiceForm({
    invoice,
    preselectedClientId,
    preselectedProjectId,
    onSuccess,
    onOpenChange,
    open,
  })
  // @ts-expect-error - Type compatibility issue with react-hook-form versions
  const { calculateTotals, handleItemChange } = useInvoiceCalculations({ form })
  
  // Watch form values for calculations
  const watchedItems = form.watch("items")
  const watchedTaxAmount = form.watch("tax_amount")
  const watchedCurrency = form.watch("currency")
  const watchedStatus = form.watch("status")

  // Calculate totals when items or tax change
  useEffect(() => {
    calculateTotals()
  }, [watchedItems, watchedTaxAmount, calculateTotals])

  // Set selected client when form data changes
  useEffect(() => {
    if (open && (invoice?.client_id || preselectedClientId)) {
      setSelectedClientId(invoice?.client_id || preselectedClientId || "")
    }
  }, [invoice, preselectedClientId, open])

  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId)
  }

  const handleProjectReset = () => {
    form.setValue("project_id", "none")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Invoice" : "Create New Invoice"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update the invoice information below." 
              : "Fill in the details to create a new invoice."
            }
          </DialogDescription>
        </DialogHeader>

        {loadingData ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading form data...</span>
          </div>
        ) : (
          <Form {...form}>
            {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Client, Project, and Currency Selection */}
              {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
              <InvoiceClientProjectSelector
                control={form.control}
                clients={clients}
                projects={projects}
                selectedClientId={selectedClientId}
                onClientChange={handleClientChange}
                onProjectReset={handleProjectReset}
                loadProjectsForClient={loadProjectsForClient}
              />

              {/* Invoice Items */}
              {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
              <InvoiceItemsManager
                control={form.control}
                fields={fields}
                onAddItem={addItem}
                onRemoveItem={removeItem}
                onItemChange={handleItemChange}
              />

              {/* Tax, Totals, and Additional Fields */}
              <div className="grid grid-cols-2 gap-4">
                {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
                <InvoiceDatesAndStatus
                  control={form.control}
                  watchStatus={watchedStatus}
                />

                {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
                <InvoiceTotalsSection
                  control={form.control}
                  subtotal={Number(form.watch("amount")) || 0}
                  totalAmount={Number(form.watch("total_amount")) || 0}
                  currency={watchedCurrency}
                  onTaxChange={handleItemChange}
                />
              </div>

              {/* Notes */}
              {/* @ts-expect-error - Type compatibility issue with react-hook-form versions */}
              <InvoiceNotesSection control={form.control} />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading || loadingData}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEditing ? "Update Invoice" : "Create Invoice"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
