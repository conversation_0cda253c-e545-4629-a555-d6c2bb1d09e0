"use client"

import { Control } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { InvoiceFormData } from "@/lib/validations"
import { formatCurrency, type Currency } from "@/lib/utils/currency"

interface InvoiceTotalsSectionProps {
  control: Control<InvoiceFormData>
  subtotal: number
  totalAmount: number
  currency: Currency
  onTaxChange: (callback: () => void) => void
}

export function InvoiceTotalsSection({
  control,
  subtotal,
  totalAmount,
  currency,
  onTaxChange,
}: InvoiceTotalsSectionProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>{formatCurrency(subtotal || 0, currency)}</span>
        </div>

        <FormField
          control={control}
          name="tax_amount"
          render={({ field }) => (
            <FormItem>
              <div className="flex justify-between items-center">
                <FormLabel>Tax Amount:</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    className="w-24 text-right"
                    {...field}
                    onChange={(e) => onTaxChange(() => {
                      field.onChange(Number(e.target.value))
                    })}
                  />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between font-bold text-lg border-t pt-2">
          <span>Total:</span>
          <span>{formatCurrency(totalAmount || 0, currency)}</span>
        </div>
      </div>
    </div>
  )
}
