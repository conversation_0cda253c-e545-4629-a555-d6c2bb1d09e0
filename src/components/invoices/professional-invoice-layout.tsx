"use client"

import React from "react"
import Image from "next/image"
import { formatInvoiceCurrency } from "@/lib/utils/currency"
import { InvoiceWithRelations, InvoiceItem } from "@/lib/types"
import { CompanySettings, BankAccount, InvoiceTemplate } from "@/lib/types/company"
import { format } from "date-fns"
import { id as idLocale } from "date-fns/locale"

// Type guard for invoice items
function isInvoiceItemArray(items: unknown): items is InvoiceItem[] {
  return Array.isArray(items) && items.every(item =>
    typeof item === 'object' &&
    item !== null &&
    'description' in item &&
    'quantity' in item &&
    'rate' in item &&
    'amount' in item
  )
}

interface ProfessionalInvoiceLayoutProps {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
  className?: string
  isPrintMode?: boolean
}

export function ProfessionalInvoiceLayout({
  invoice,
  company,
  template,
  bankAccounts,
  className = "",
  isPrintMode = false
}: ProfessionalInvoiceLayoutProps) {
  const primaryBankAccount = bankAccounts.find(account => 
    account.is_primary && account.currency === invoice.currency
  ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    if (template?.indonesian_settings?.date_format === 'dd/mm/yyyy') {
      return format(date, 'dd/MM/yyyy')
    } else if (template?.indonesian_settings?.date_format === 'dd-mm-yyyy') {
      return format(date, 'dd-MM-yyyy')
    }
    return format(date, 'dd MMM yyyy', { locale: idLocale })
  }

  const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
  const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

  return (
    <div className={`bg-white ${isPrintMode ? 'print-layout' : ''} ${className}`}>
      {/* Modern Header */}
      <div className="invoice-header mb-12">
        <div className="flex items-center justify-between">
          {/* Company Logo */}
          <div className="flex items-center">
            {template?.layout?.show_logo && company.logo_url && (
              <div className="company-logo">
                <Image
                  src={company.logo_url}
                  alt={company.name}
                  width={200}
                  height={80}
                  className="h-12 w-auto object-contain"
                />
              </div>
            )}
          </div>

          {/* Invoice Title and Number - Modern Style */}
          <div className="text-right">
            <div className="text-sm font-medium text-gray-500 mb-1 uppercase tracking-wide">
              {showIndonesian && showEnglish ? 'Invoice / Faktur' :
               showIndonesian ? 'Faktur' : 'Invoice'}
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-2">
              #{invoice.invoice_number}
            </div>
            {invoice.milestone_type && invoice.milestone_type !== 'standard' && (
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                {invoice.milestone_type === 'dp' && (showIndonesian ? 'Uang Muka' : 'Down Payment')}
                {invoice.milestone_type === 'progress' && (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment')}
                {invoice.milestone_type === 'final' && (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment')}
                {invoice.milestone_percentage && ` (${invoice.milestone_percentage}%)`}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 my-8"></div>

      {/* Modern Client and Invoice Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-12">
        {/* Bill To - Modern Card Style */}
        <div className="bg-gray-50 rounded-lg p-6">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4">
            {showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' :
             showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
          </div>
          <div className="space-y-2">
            <p className="font-semibold text-gray-900 text-lg">{invoice.client?.name}</p>
            {invoice.client?.company && (
              <p className="text-gray-600 font-medium">{invoice.client.company}</p>
            )}
            {invoice.client?.address && (
              <div className="text-gray-500 text-sm space-y-1 mt-3">
                {typeof invoice.client.address === 'object' && (
                  <>
                    <p>{invoice.client.address.street}</p>
                    <p>{invoice.client.address.city}, {invoice.client.address.state}</p>
                    <p>{invoice.client.address.postal_code}</p>
                  </>
                )}
              </div>
            )}
            <div className="pt-3 space-y-1">
              {invoice.client?.email && (
                <p className="text-gray-600 text-sm">{invoice.client.email}</p>
              )}
              {invoice.client?.phone && (
                <p className="text-gray-600 text-sm">{invoice.client.phone}</p>
              )}
            </div>
          </div>
        </div>

        {/* Invoice Details - Clean List Style */}
        <div>
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4">
            {showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' :
             showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-600 text-sm">
                {showIndonesian ? 'Tanggal Faktur' : 'Invoice Date'}
              </span>
              <span className="font-medium text-gray-900">{formatDate(invoice.created_at)}</span>
            </div>
            {invoice.due_date && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600 text-sm">
                  {showIndonesian ? 'Jatuh Tempo' : 'Due Date'}
                </span>
                <span className="font-medium text-gray-900">{formatDate(invoice.due_date)}</span>
              </div>
            )}
            {invoice.project && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600 text-sm">
                  {showIndonesian ? 'Proyek' : 'Project'}
                </span>
                <span className="font-medium text-gray-900">{invoice.project.name}</span>
              </div>
            )}
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-600 text-sm">
                {showIndonesian ? 'Mata Uang' : 'Currency'}
              </span>
              <span className="font-medium text-gray-900">{invoice.currency}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Items Table */}
      <div className="mb-8">
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-900">
              <div className="col-span-6">
                {showIndonesian && showEnglish ? 'Description / Deskripsi' :
                 showIndonesian ? 'Deskripsi' : 'Description'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Jumlah' : 'Qty'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Harga' : 'Rate'}
              </div>
              <div className="col-span-2 text-right">
                {showIndonesian ? 'Total' : 'Amount'}
              </div>
            </div>
          </div>
          
          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {invoice.items && isInvoiceItemArray(invoice.items) && invoice.items.map((item, index) => (
              <div key={index} className="px-6 py-4">
                <div className="grid grid-cols-12 gap-4 text-sm">
                  <div className="col-span-6 text-gray-900">{item.description}</div>
                  <div className="col-span-2 text-center text-gray-700">{item.quantity}</div>
                  <div className="col-span-2 text-center text-gray-700">
                    {formatInvoiceCurrency(item.rate, invoice.currency as 'IDR' | 'USD')}
                  </div>
                  <div className="col-span-2 text-right font-medium text-gray-900">
                    {formatInvoiceCurrency(item.amount, invoice.currency as 'IDR' | 'USD')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Totals Section */}
      <div className="flex justify-end mb-8">
        <div className="w-full max-w-sm">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {showIndonesian ? 'Subtotal:' : 'Subtotal:'}
              </span>
              <span className="font-medium">
                {formatInvoiceCurrency(invoice.amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>

            {invoice.tax_amount > 0 && (
              <div className="flex justify-between py-2">
                <span className="text-gray-600">
                  {showIndonesian ? 'Pajak:' : 'Tax:'}
                </span>
                <span className="font-medium">
                  {formatInvoiceCurrency(invoice.tax_amount, invoice.currency as 'IDR' | 'USD')}
                </span>
              </div>
            )}

            <div className="h-px bg-gray-200 my-2"></div>

            <div className="flex justify-between py-3 text-lg font-bold">
              <span className="text-gray-900">
                {showIndonesian ? 'Total:' : 'Total:'}
              </span>
              <span className="text-gray-900">
                {formatInvoiceCurrency(invoice.total_amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Invoice Notes (only if specified on the invoice) */}
      {invoice.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {showIndonesian ? 'Catatan' : 'Notes'}
          </h3>
          <p className="text-sm text-gray-600">{invoice.notes}</p>
        </div>
      )}

      {/* Modern Simplified Footer */}
      <div className="mt-16 pt-8 border-t border-gray-200">
        {/* Essential Payment Information Only */}
        {template?.content?.show_bank_details && primaryBankAccount && (
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <div className="text-center">
              <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                {showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}
              </div>
              <div className="space-y-2">
                <p className="font-semibold text-gray-900 text-lg">{primaryBankAccount.bank_name}</p>
                <div className="flex justify-center items-center space-x-8 text-sm">
                  <div className="text-center">
                    <p className="text-gray-500 text-xs uppercase tracking-wide">
                      {showIndonesian ? 'Nama Rekening' : 'Account Name'}
                    </p>
                    <p className="font-medium text-gray-900">{primaryBankAccount.account_name}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-500 text-xs uppercase tracking-wide">
                      {showIndonesian ? 'Nomor Rekening' : 'Account Number'}
                    </p>
                    <p className="font-mono font-bold text-gray-900 text-lg">{primaryBankAccount.account_number}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Minimal Company Footer */}
        <div className="text-center space-y-2">
          <p className="font-medium text-gray-900">{company.name}</p>
          <div className="flex justify-center items-center space-x-4 text-sm text-gray-500">
            <span>{company.email}</span>
            <span>•</span>
            <span>{company.phone}</span>
            {company.website && (
              <>
                <span>•</span>
                <span>{company.website}</span>
              </>
            )}
          </div>
          {template?.content?.show_tax_id && company.tax_id && (
            <p className="text-xs text-gray-400">NPWP: {company.tax_id}</p>
          )}
        </div>

        {/* Footer Text */}
        {company.invoice_settings?.invoice_footer_text && (
          <div className="text-center mt-6 pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-400">
              {company.invoice_settings.invoice_footer_text}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
