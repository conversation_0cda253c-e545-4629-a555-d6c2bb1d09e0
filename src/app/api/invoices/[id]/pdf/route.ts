import { NextRequest, NextResponse } from 'next/server'
import { getInvoiceWithCompanyData } from '@/lib/api/invoice-pdf'
import { InvoicePDFGenerator } from '@/lib/pdf/invoice-pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const invoiceId = params.id

    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      )
    }

    console.log('Generating PDF for invoice:', invoiceId)

    // Get invoice data with company information
    const data = await getInvoiceWithCompanyData(invoiceId)

    if (!data.invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    console.log('Invoice data retrieved successfully')

    // For now, return HTML preview until Puppeteer is installed
    const html = InvoicePDFGenerator.generateInvoiceHTML(data)

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    })

    // TODO: Uncomment when P<PERSON>peteer is installed
    /*
    // Generate PDF
    const pdfBuffer = await InvoicePDFGenerator.generatePDF(data)

    // Return PDF response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${data.invoice.invoice_number}.pdf"`,
      },
    })
    */
  } catch (error) {
    console.error('PDF generation error:', error)

    // Return more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

    return NextResponse.json(
      {
        error: 'Failed to generate PDF',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
