import { ThemeToggle } from "@/components/theme-toggle"

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen flex">
      {/* Left side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary text-primary-foreground p-8 flex-col justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-4">HarunStudio</h1>
          <p className="text-lg opacity-90">
            Comprehensive business management platform for creative agencies
          </p>
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-primary-foreground rounded-full"></div>
            <span>Client & Project Management</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-primary-foreground rounded-full"></div>
            <span>Invoice Generation & Tracking</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-primary-foreground rounded-full"></div>
            <span>Analytics & Reporting</span>
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-6">
          <div className="flex justify-end">
            <ThemeToggle />
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
