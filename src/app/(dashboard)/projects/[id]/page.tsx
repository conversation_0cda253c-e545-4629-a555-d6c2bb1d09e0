"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { StatusBadge } from "@/components/ui/status-badge"
import { PageHeader } from "@/components/layout/page-header"
import { ProjectForm } from "@/components/projects/project-form"
import { PaymentMilestonesManager } from "@/components/invoices/payment-milestones-manager"
import { getProject, deleteProject } from "@/lib/api/projects-client"
import { Project } from "@/lib/types"
import { ArrowLeft, Edit, Trash2, Calendar, DollarSign, Users, Building, Mail, Phone } from "lucide-react"
import { formatDistanceToNow, format } from "date-fns"

export default function ProjectDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  const fetchProject = async () => {
    if (!params.id) return
    
    setLoading(true)
    try {
      const data = await getProject(params.id as string)
      setProject(data)
    } catch (error) {
      console.error("Failed to fetch project:", error)
      router.push("/projects")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProject()
  }, [params.id])

  const handleDeleteProject = async () => {
    if (!project) return
    
    if (confirm("Are you sure you want to delete this project? This action cannot be undone.")) {
      try {
        await deleteProject(project.id)
        router.push("/projects")
      } catch (error) {
        console.error("Failed to delete project:", error)
      }
    }
  }

  const handleEditSuccess = () => {
    fetchProject()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="h-48 bg-muted rounded"></div>
            <div className="h-48 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">Project not found.</p>
          <Button onClick={() => router.push("/projects")} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title={project.name}
        description={`Project details and progress tracking`}
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push("/projects")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowEditForm(true)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteProject}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </PageHeader>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Project Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Project Information
              <StatusBadge status={project.status} />
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {project.description && (
                <div>
                  <p className="text-sm font-medium">Description</p>
                  <p className="text-sm text-muted-foreground">{project.description}</p>
                </div>
              )}

              {project.budget && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Budget:</span>
                  <span className="text-sm">{formatCurrency(project.budget)}</span>
                </div>
              )}

              {project.start_date && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Start Date:</span>
                  <span className="text-sm">{format(new Date(project.start_date), 'PPP')}</span>
                </div>
              )}

              {project.end_date && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">End Date:</span>
                  <span className="text-sm">{format(new Date(project.end_date), 'PPP')}</span>
                </div>
              )}

              <Separator />

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>
                  Created {formatDistanceToNow(new Date(project.created_at), { addSuffix: true })}
                </span>
              </div>

              {project.created_by_user && (
                <div className="text-sm text-muted-foreground">
                  Created by {project.created_by_user.full_name}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Client Information */}
        <Card>
          <CardHeader>
            <CardTitle>Client Information</CardTitle>
          </CardHeader>
          <CardContent>
            {project.client ? (
              <div className="space-y-3">
                <div>
                  <p className="font-medium">{project.client.name}</p>
                  {project.client.company && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Building className="h-4 w-4" />
                      <span>{project.client.company}</span>
                    </div>
                  )}
                </div>

                {project.client.email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{project.client.email}</span>
                  </div>
                )}

                {project.client.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{project.client.phone}</span>
                  </div>
                )}

                <Button variant="outline" size="sm" asChild>
                  <a href={`/clients/${project.client.id}`}>
                    View Client Details
                  </a>
                </Button>
              </div>
            ) : (
              <p className="text-muted-foreground">No client assigned</p>
            )}
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card>
          <CardHeader>
            <CardTitle>Team Members</CardTitle>
            <CardDescription>
              People assigned to this project
            </CardDescription>
          </CardHeader>
          <CardContent>
            {project.assigned_team_members && project.assigned_team_members.length > 0 ? (
              <div className="space-y-3">
                {project.assigned_team_members.map((member) => (
                  <div key={member.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{member.full_name}</p>
                      <p className="text-sm text-muted-foreground">{member.role || 'Team Member'}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No team members assigned yet.</p>
                <Button variant="outline" className="mt-2" onClick={() => setShowEditForm(true)}>
                  Assign Team Members
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Milestones */}
        <div className="md:col-span-2">
          <PaymentMilestonesManager
            projectId={project.id}
            projectName={project.name}
            projectBudget={project.budget}
            onMilestoneUpdate={fetchProject}
          />
        </div>
      </div>

      {/* Edit Form Dialog */}
      <ProjectForm
        open={showEditForm}
        onOpenChange={setShowEditForm}
        project={project}
        onSuccess={handleEditSuccess}
      />
    </div>
  )
}
