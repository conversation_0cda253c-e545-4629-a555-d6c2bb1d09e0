import { redirect } from "next/navigation"
import { getCurrentUser, getUserProfile } from "@/lib/auth/actions"
import { AppSidebar } from "@/components/layout/app-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect("/login")
  }
  
  const profile = await getUserProfile()
  
  if (!profile) {
    redirect("/login")
  }

  return (
    <SidebarProvider>
      <AppSidebar user={profile} />
      <SidebarInset>
        <div className="flex flex-1 flex-col">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
