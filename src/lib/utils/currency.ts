export type Currency = 'USD' | 'IDR'

export interface CurrencyConfig {
  code: Currency
  symbol: string
  name: string
  locale: string
  minimumFractionDigits: number
  maximumFractionDigits: number
}

export const CURRENCIES: Record<Currency, CurrencyConfig> = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    locale: 'en-US',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  },
  IDR: {
    code: 'IDR',
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    locale: 'id-ID',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
}

// Default currency for the application (Indonesian business)
export const DEFAULT_CURRENCY: Currency = 'IDR'

/**
 * Format a number as currency with proper localization
 * @param amount - The amount to format
 * @param currency - The currency code (defaults to IDR)
 * @param options - Additional formatting options
 */
export function formatCurrency(
  amount: number,
  currency: Currency = DEFAULT_CURRENCY,
  options?: {
    showSymbol?: boolean
    compact?: boolean
    locale?: string
  }
): string {
  const config = CURRENCIES[currency]
  const locale = options?.locale || config.locale

  // Handle invalid amounts
  if (typeof amount !== 'number' || isNaN(amount)) {
    amount = 0
  }

  // For compact formatting (e.g., "4M" instead of "4,000,000")
  if (options?.compact && Math.abs(amount) >= 1000000) {
    const formatter = new Intl.NumberFormat(locale, {
      notation: 'compact',
      compactDisplay: 'short',
      minimumFractionDigits: 0,
      maximumFractionDigits: 1,
    })
    
    const formatted = formatter.format(amount)
    return options?.showSymbol !== false ? `${config.symbol} ${formatted}` : formatted
  }

  // Standard formatting
  const formatter = new Intl.NumberFormat(locale, {
    style: options?.showSymbol !== false ? 'currency' : 'decimal',
    currency: currency,
    minimumFractionDigits: config.minimumFractionDigits,
    maximumFractionDigits: config.maximumFractionDigits,
  })

  let formatted = formatter.format(amount)

  // For IDR, we want to use "Rp" instead of "IDR" symbol
  if (currency === 'IDR' && options?.showSymbol !== false) {
    formatted = formatted.replace(/IDR\s?/, 'Rp ')
  }

  return formatted
}

/**
 * Format currency for display in forms (without currency symbol)
 * @param amount - The amount to format
 * @param currency - The currency code
 */
export function formatCurrencyInput(amount: number, currency: Currency = DEFAULT_CURRENCY): string {
  return formatCurrency(amount, currency, { showSymbol: false })
}

/**
 * Format currency for compact display (e.g., in cards or lists)
 * @param amount - The amount to format
 * @param currency - The currency code
 */
export function formatCurrencyCompact(amount: number, currency: Currency = DEFAULT_CURRENCY): string {
  return formatCurrency(amount, currency, { compact: true })
}

/**
 * Parse a currency string back to a number
 * @param value - The currency string to parse
 * @param currency - The currency code for context
 */
export function parseCurrency(value: string, currency: Currency = DEFAULT_CURRENCY): number {
  if (!value || typeof value !== 'string') return 0

  // Remove currency symbols and spaces
  const config = CURRENCIES[currency]
  let cleaned = value
    .replace(new RegExp(config.symbol, 'g'), '')
    .replace(/[^\d.,\-]/g, '')
    .trim()

  // Handle different decimal separators based on locale
  if (config.locale === 'id-ID') {
    // Indonesian format: 1.000.000,50 (dots for thousands, comma for decimal)
    // But for IDR, we typically don't use decimals
    cleaned = cleaned.replace(/\./g, '').replace(/,/g, '.')
  } else {
    // US format: 1,000,000.50 (commas for thousands, dot for decimal)
    cleaned = cleaned.replace(/,/g, '')
  }

  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? 0 : parsed
}

/**
 * Get currency symbol for a given currency
 * @param currency - The currency code
 */
export function getCurrencySymbol(currency: Currency): string {
  return CURRENCIES[currency]?.symbol || '$'
}

/**
 * Get currency name for a given currency
 * @param currency - The currency code
 */
export function getCurrencyName(currency: Currency): string {
  return CURRENCIES[currency]?.name || 'Unknown Currency'
}

/**
 * Get all available currencies for selection
 */
export function getAvailableCurrencies(): CurrencyConfig[] {
  return Object.values(CURRENCIES)
}

/**
 * Convert amount between currencies (placeholder for future exchange rate integration)
 * @param amount - The amount to convert
 * @param fromCurrency - Source currency
 * @param toCurrency - Target currency
 */
export function convertCurrency(
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency
): number {
  // For now, return the same amount since we don't have exchange rate integration
  // In the future, this could integrate with exchange rate APIs
  if (fromCurrency === toCurrency) return amount
  
  // Placeholder conversion rates (these should come from an API in production)
  const exchangeRates: Record<string, number> = {
    'USD_IDR': 15000, // 1 USD = 15,000 IDR (approximate)
    'IDR_USD': 1 / 15000,
  }
  
  const rateKey = `${fromCurrency}_${toCurrency}`
  const rate = exchangeRates[rateKey] || 1
  
  return amount * rate
}

/**
 * Format a range of currency values
 * @param min - Minimum amount
 * @param max - Maximum amount
 * @param currency - Currency code
 */
export function formatCurrencyRange(
  min: number,
  max: number,
  currency: Currency = DEFAULT_CURRENCY
): string {
  if (min === max) {
    return formatCurrency(min, currency)
  }
  
  const minFormatted = formatCurrency(min, currency)
  const maxFormatted = formatCurrency(max, currency, { showSymbol: false })
  
  return `${minFormatted} - ${maxFormatted}`
}

/**
 * Validate if a currency code is supported
 * @param currency - Currency code to validate
 */
export function isValidCurrency(currency: string): currency is Currency {
  return currency in CURRENCIES
}

/**
 * Get appropriate step value for currency input fields
 * @param currency - Currency code
 */
export function getCurrencyStep(currency: Currency = DEFAULT_CURRENCY): string {
  const config = CURRENCIES[currency]
  return config.minimumFractionDigits > 0 ? '0.01' : '1'
}

/**
 * Format currency for invoice display with proper Indonesian formatting
 * @param amount - Amount to format
 * @param currency - Currency code
 */
export function formatInvoiceCurrency(amount: number, currency: Currency = DEFAULT_CURRENCY): string {
  if (currency === 'IDR') {
    // Indonesian Rupiah formatting: Rp 4.000.000
    const formatted = new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
    
    return `Rp ${formatted}`
  }
  
  return formatCurrency(amount, currency)
}
