/**
 * High-Quality Invoice PDF Generator using Puppeteer
 * 
 * This implementation provides professional PDF generation with:
 * - High-quality rendering
 * - Proper Indonesian formatting
 * - Professional typography
 * - Optimized for business use
 */

import { InvoiceWithRelations } from '@/lib/types'
import { CompanySettings, BankAccount, InvoiceTemplate } from '@/lib/types/company'

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margin?: {
    top: string
    right: string
    bottom: string
    left: string
  }
  displayHeaderFooter?: boolean
  headerTemplate?: string
  footerTemplate?: string
  printBackground?: boolean
  scale?: number
}

export interface InvoicePDFData {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
}

export class InvoicePDFGenerator {
  private static defaultOptions: PDFGenerationOptions = {
    format: 'A4',
    orientation: 'portrait',
    margin: {
      top: '20mm',
      right: '15mm',
      bottom: '20mm',
      left: '15mm'
    },
    displayHeaderFooter: false,
    printBackground: true,
    scale: 1.0
  }

  /**
   * Generate PDF buffer from invoice data
   */
  static async generatePDF(
    data: InvoicePDFData, 
    options: Partial<PDFGenerationOptions> = {}
  ): Promise<Buffer> {
    // This will be implemented with Puppeteer
    // For now, return a placeholder
    throw new Error('PDF generation not yet implemented. Please install Puppeteer first.')
  }

  /**
   * Generate HTML content for PDF rendering
   */
  static generateInvoiceHTML(data: InvoicePDFData): string {
    const { invoice, company, template, bankAccounts } = data
    
    const primaryBankAccount = bankAccounts.find(account => 
      account.is_primary && account.currency === invoice.currency
    ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

    const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
    const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoice_number}</title>
    <style>
        ${this.getInvoiceCSS()}
    </style>
</head>
<body>
    <div class="invoice-container">
        ${this.generateHeaderHTML(invoice, company, template, showIndonesian, showEnglish)}
        ${this.generateDetailsHTML(invoice, company, showIndonesian, showEnglish)}
        ${this.generateItemsHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateTotalsHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateNotesHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateFooterHTML(company, primaryBankAccount, template, showIndonesian, showEnglish)}
    </div>
</body>
</html>`
  }

  /**
   * Generate CSS styles for PDF
   */
  private static getInvoiceCSS(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #1f2937;
            background: white;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }
        
        .company-info h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
        }
        
        .invoice-title {
            text-align: right;
        }
        
        .invoice-title h2 {
            font-size: 2.25rem;
            font-weight: 800;
            color: #111827;
            margin-bottom: 0.5rem;
        }
        
        .details-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        
        .items-table th {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #111827;
        }
        
        .items-table td {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            color: #374151;
        }
        
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 2rem;
        }
        
        .totals-table {
            width: 300px;
        }
        
        .footer {
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .footer h4 {
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.75rem;
        }
        
        .footer p {
            margin-bottom: 0.25rem;
            color: #374151;
            font-size: 0.875rem;
        }
        
        @media print {
            body { margin: 0; }
            .invoice-container { max-width: none; }
        }
    `
  }

  private static generateHeaderHTML(
    invoice: InvoiceWithRelations, 
    company: CompanySettings, 
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <div class="header">
            <div class="company-info">
                <h1>${company.name}</h1>
                ${company.legal_name && company.legal_name !== company.name ? `<p>${company.legal_name}</p>` : ''}
                <div style="margin-top: 1rem; font-size: 0.875rem; color: #6b7280;">
                    <p>${company.address.street}</p>
                    <p>${company.address.city}, ${company.address.state} ${company.address.postal_code}</p>
                    <p>${company.address.country}</p>
                    <p>Email: ${company.email}</p>
                    <p>Phone: ${company.phone}</p>
                    ${company.website ? `<p>Website: ${company.website}</p>` : ''}
                </div>
            </div>
            <div class="invoice-title">
                <h2>${showIndonesian && showEnglish ? 'INVOICE / FAKTUR' : showIndonesian ? 'FAKTUR' : 'INVOICE'}</h2>
                <div style="font-size: 1.125rem; font-weight: 600; color: #374151;">
                    #${invoice.invoice_number}
                </div>
                ${invoice.milestone_type && invoice.milestone_type !== 'standard' ? `
                    <div style="margin-top: 0.5rem; padding: 0.25rem 0.75rem; background: #dbeafe; color: #1e40af; border-radius: 0.375rem; font-size: 0.875rem; display: inline-block;">
                        ${invoice.milestone_type === 'dp' ? (showIndonesian ? 'Uang Muka' : 'Down Payment') : ''}
                        ${invoice.milestone_type === 'progress' ? (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment') : ''}
                        ${invoice.milestone_type === 'final' ? (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment') : ''}
                        ${invoice.milestone_percentage ? ` (${invoice.milestone_percentage}%)` : ''}
                    </div>
                ` : ''}
            </div>
        </div>
    `
  }

  private static generateDetailsHTML(
    invoice: InvoiceWithRelations,
    company: CompanySettings,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <div class="details-section">
            <div>
                <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 0.75rem; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">
                    ${showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' : showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
                </h3>
                <div style="font-size: 0.875rem;">
                    <p style="font-weight: 600; color: #111827; margin-bottom: 0.25rem;">${invoice.client?.name || ''}</p>
                    ${invoice.client?.company ? `<p style="color: #374151; margin-bottom: 0.25rem;">${invoice.client.company}</p>` : ''}
                    ${invoice.client?.email ? `<p style="color: #6b7280; margin-bottom: 0.25rem;">Email: ${invoice.client.email}</p>` : ''}
                    ${invoice.client?.phone ? `<p style="color: #6b7280; margin-bottom: 0.25rem;">Phone: ${invoice.client.phone}</p>` : ''}
                </div>
            </div>
            <div>
                <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 0.75rem; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">
                    ${showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' : showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
                </h3>
                <div style="font-size: 0.875rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span style="color: #6b7280;">${showIndonesian ? 'Tanggal Faktur:' : 'Invoice Date:'}</span>
                        <span style="font-weight: 600;">${new Date(invoice.created_at).toLocaleDateString('id-ID')}</span>
                    </div>
                    ${invoice.due_date ? `
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span style="color: #6b7280;">${showIndonesian ? 'Jatuh Tempo:' : 'Due Date:'}</span>
                            <span style="font-weight: 600;">${new Date(invoice.due_date).toLocaleDateString('id-ID')}</span>
                        </div>
                    ` : ''}
                    ${invoice.project ? `
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span style="color: #6b7280;">${showIndonesian ? 'Proyek:' : 'Project:'}</span>
                            <span style="font-weight: 600;">${invoice.project.name}</span>
                        </div>
                    ` : ''}
                    <div style="display: flex; justify-content: space-between;">
                        <span style="color: #6b7280;">${showIndonesian ? 'Mata Uang:' : 'Currency:'}</span>
                        <span style="font-weight: 600;">${invoice.currency}</span>
                    </div>
                </div>
            </div>
        </div>
    `
  }

  private static generateItemsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    const items = Array.isArray(invoice.items) ? invoice.items : []
    
    return `
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50%;">${showIndonesian && showEnglish ? 'Description / Deskripsi' : showIndonesian ? 'Deskripsi' : 'Description'}</th>
                    <th style="width: 16.67%; text-align: center;">${showIndonesian ? 'Jumlah' : 'Qty'}</th>
                    <th style="width: 16.67%; text-align: center;">${showIndonesian ? 'Harga' : 'Rate'}</th>
                    <th style="width: 16.67%; text-align: right;">${showIndonesian ? 'Total' : 'Amount'}</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((item: any) => `
                    <tr>
                        <td>${item.description || ''}</td>
                        <td style="text-align: center;">${item.quantity || 0}</td>
                        <td style="text-align: center;">${this.formatCurrency(item.rate || 0, invoice.currency)}</td>
                        <td style="text-align: right; font-weight: 600;">${this.formatCurrency(item.amount || 0, invoice.currency)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `
  }

  private static generateTotalsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td style="padding: 0.5rem 0; color: #6b7280;">${showIndonesian ? 'Subtotal:' : 'Subtotal:'}</td>
                    <td style="padding: 0.5rem 0; text-align: right; font-weight: 600;">${this.formatCurrency(invoice.amount, invoice.currency)}</td>
                </tr>
                ${invoice.tax_amount > 0 ? `
                    <tr>
                        <td style="padding: 0.5rem 0; color: #6b7280;">${showIndonesian ? 'Pajak:' : 'Tax:'}</td>
                        <td style="padding: 0.5rem 0; text-align: right; font-weight: 600;">${this.formatCurrency(invoice.tax_amount, invoice.currency)}</td>
                    </tr>
                ` : ''}
                <tr style="border-top: 2px solid #374151; border-bottom: 3px double #374151;">
                    <td style="padding: 0.75rem 0; font-size: 1.125rem; font-weight: 700; color: #111827;">${showIndonesian ? 'Total:' : 'Total:'}</td>
                    <td style="padding: 0.75rem 0; text-align: right; font-size: 1.125rem; font-weight: 700; color: #111827;">${this.formatCurrency(invoice.total_amount, invoice.currency)}</td>
                </tr>
            </table>
        </div>
    `
  }

  private static generateNotesHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    if (!invoice.notes) return ''
    
    return `
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin-bottom: 0.5rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.25rem;">
                ${showIndonesian ? 'Catatan' : 'Notes'}
            </h3>
            <p style="color: #374151; line-height: 1.6;">${invoice.notes}</p>
        </div>
    `
  }

  private static generateFooterHTML(
    company: CompanySettings,
    primaryBankAccount: any,
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <div class="footer">
            <div>
                <h4>${showIndonesian && showEnglish ? 'Company Information / Informasi Perusahaan' : showIndonesian ? 'Informasi Perusahaan' : 'Company Information'}</h4>
                <p style="font-weight: 600; color: #111827;">${company.name}</p>
                ${company.legal_name && company.legal_name !== company.name ? `<p>${company.legal_name}</p>` : ''}
                <p>${company.address.street}</p>
                <p>${company.address.city}, ${company.address.state} ${company.address.postal_code}</p>
                <p>${company.address.country}</p>
                <p>Email: ${company.email}</p>
                <p>Phone: ${company.phone}</p>
                ${company.website ? `<p>Website: ${company.website}</p>` : ''}
                ${template?.content?.show_tax_id && company.tax_id ? `<p>NPWP: ${company.tax_id}</p>` : ''}
                ${template?.content?.show_business_license && company.business_license ? `<p>NIB: ${company.business_license}</p>` : ''}
            </div>
            ${primaryBankAccount ? `
                <div>
                    <h4>${showIndonesian && showEnglish ? 'Payment Information / Informasi Pembayaran' : showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}</h4>
                    <p style="font-weight: 600; color: #111827;">${primaryBankAccount.bank_name}</p>
                    <p>${showIndonesian ? 'Nama Rekening:' : 'Account Name:'} ${primaryBankAccount.account_name}</p>
                    <p>${showIndonesian ? 'Nomor Rekening:' : 'Account Number:'} ${primaryBankAccount.account_number}</p>
                    <p>${showIndonesian ? 'Mata Uang:' : 'Currency:'} ${primaryBankAccount.currency}</p>
                    ${primaryBankAccount.swift_code ? `<p>SWIFT: ${primaryBankAccount.swift_code}</p>` : ''}
                    ${primaryBankAccount.branch ? `<p>${showIndonesian ? 'Cabang:' : 'Branch:'} ${primaryBankAccount.branch}</p>` : ''}
                </div>
            ` : ''}
        </div>
        ${company.invoice_settings?.invoice_footer_text ? `
            <div style="text-center; margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #f3f4f6;">
                <p style="color: #6b7280; font-size: 0.875rem;">${company.invoice_settings.invoice_footer_text}</p>
            </div>
        ` : ''}
    `
  }

  private static formatCurrency(amount: number, currency: string): string {
    if (currency === 'IDR') {
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }
}
