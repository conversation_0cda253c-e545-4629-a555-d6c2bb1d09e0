/**
 * High-Quality Invoice PDF Generator using Puppeteer
 * 
 * This implementation provides professional PDF generation with:
 * - High-quality rendering
 * - Proper Indonesian formatting
 * - Professional typography
 * - Optimized for business use
 */

import { InvoiceWithRelations } from '@/lib/types'
import { CompanySettings, BankAccount, InvoiceTemplate } from '@/lib/types/company'

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margin?: {
    top: string
    right: string
    bottom: string
    left: string
  }
  displayHeaderFooter?: boolean
  headerTemplate?: string
  footerTemplate?: string
  printBackground?: boolean
  scale?: number
}

export interface InvoicePDFData {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
}

export class InvoicePDFGenerator {
  private static defaultOptions: PDFGenerationOptions = {
    format: 'A4',
    orientation: 'portrait',
    margin: {
      top: '20mm',
      right: '15mm',
      bottom: '20mm',
      left: '15mm'
    },
    displayHeaderFooter: false,
    printBackground: true,
    scale: 1.0
  }

  /**
   * Generate PDF buffer from invoice data
   */
  static async generatePDF(
    data: InvoicePDFData,
    options: Partial<PDFGenerationOptions> = {}
  ): Promise<Buffer> {
    const puppeteer = await import('puppeteer')

    // Merge options with defaults
    const mergedOptions = { ...this.defaultOptions, ...options }

    // Launch browser with optimized settings for server environment
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    })

    try {
      const page = await browser.newPage()

      // Generate HTML content
      const html = this.generateInvoiceHTML(data)

      // Set content and wait for fonts and images to load
      await page.setContent(html, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })

      // Wait for images to load if there are any
      await page.evaluate(() => {
        const images = Array.from(document.images)
        return Promise.all(images.map(img => {
          if (img.complete) return Promise.resolve()
          return new Promise((resolve, reject) => {
            img.addEventListener('load', resolve)
            img.addEventListener('error', reject)
            // Set a timeout for image loading
            setTimeout(reject, 10000)
          })
        }))
      }).catch(() => {
        // Continue even if some images fail to load
        console.warn('Some images failed to load in PDF generation')
      })

      // Generate PDF with proper options
      const pdfBuffer = await page.pdf({
        format: mergedOptions.format,
        landscape: mergedOptions.orientation === 'landscape',
        margin: mergedOptions.margin,
        displayHeaderFooter: mergedOptions.displayHeaderFooter,
        headerTemplate: mergedOptions.headerTemplate,
        footerTemplate: mergedOptions.footerTemplate,
        printBackground: mergedOptions.printBackground,
        scale: mergedOptions.scale,
        preferCSSPageSize: true,
        timeout: 30000
      })

      return Buffer.from(pdfBuffer)
    } catch (error) {
      console.error('PDF generation error:', error)
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      await browser.close()
    }
  }

  /**
   * Generate HTML content for PDF rendering
   */
  static generateInvoiceHTML(data: InvoicePDFData): string {
    const { invoice, company, template, bankAccounts } = data
    
    const primaryBankAccount = bankAccounts.find(account => 
      account.is_primary && account.currency === invoice.currency
    ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

    const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
    const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoice_number}</title>
    <style>
        ${this.getInvoiceCSS()}
    </style>
</head>
<body>
    <div class="invoice-container">
        ${this.generateHeaderHTML(invoice, company, template, showIndonesian, showEnglish)}
        ${this.generateSeparatorHTML()}
        ${this.generateDetailsHTML(invoice, company, showIndonesian, showEnglish)}
        ${this.generateItemsHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateTotalsHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateNotesHTML(invoice, showIndonesian, showEnglish)}
        ${this.generateFooterHTML(company, primaryBankAccount, template, showIndonesian, showEnglish)}
    </div>
</body>
</html>`
  }

  /**
   * Generate CSS styles for PDF
   */
  private static getInvoiceCSS(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #1f2937;
            background: white;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }
        
        .company-info h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
        }
        
        .invoice-title {
            text-align: right;
        }
        
        .invoice-title h2 {
            font-size: 2.25rem;
            font-weight: 800;
            color: #111827;
            margin-bottom: 0.5rem;
        }
        
        .details-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        
        .items-table th {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #111827;
        }
        
        .items-table td {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            color: #374151;
        }
        
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 2rem;
        }
        
        .totals-table {
            width: 300px;
        }
        
        .footer {
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .footer h4 {
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.75rem;
        }
        
        .footer p {
            margin-bottom: 0.25rem;
            color: #374151;
            font-size: 0.875rem;
        }
        
        @media print {
            body { margin: 0; }
            .invoice-container { max-width: none; }
        }
    `
  }

  private static generateHeaderHTML(
    invoice: InvoiceWithRelations,
    company: CompanySettings,
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <!-- Modern Header - matches updated ProfessionalInvoiceLayout -->
        <div class="invoice-header" style="margin-bottom: 3rem;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <!-- Company Logo -->
                <div style="display: flex; align-items: center;">
                    ${template?.layout?.show_logo && company.logo_url ? `
                        <div class="company-logo">
                            <img src="${company.logo_url}" alt="${company.name}" style="height: 48px; width: auto; object-fit: contain;" />
                        </div>
                    ` : ''}
                </div>

                <!-- Invoice Title and Number - Modern Style -->
                <div style="text-align: right;">
                    <div style="font-size: 0.875rem; font-weight: 500; color: #6b7280; margin-bottom: 0.25rem; text-transform: uppercase; letter-spacing: 0.05em;">
                        ${showIndonesian && showEnglish ? 'Invoice / Faktur' : showIndonesian ? 'Faktur' : 'Invoice'}
                    </div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">
                        #${invoice.invoice_number}
                    </div>
                    ${invoice.milestone_type && invoice.milestone_type !== 'standard' ? `
                        <div style="display: inline-flex; align-items: center; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; background: #eff6ff; color: #1d4ed8; border: 1px solid #dbeafe;">
                            ${invoice.milestone_type === 'dp' ? (showIndonesian ? 'Uang Muka' : 'Down Payment') : ''}
                            ${invoice.milestone_type === 'progress' ? (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment') : ''}
                            ${invoice.milestone_type === 'final' ? (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment') : ''}
                            ${invoice.milestone_percentage ? ` (${invoice.milestone_percentage}%)` : ''}
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `
  }

  /**
   * Generate modern separator line
   */
  private static generateSeparatorHTML(): string {
    return `
        <div style="margin: 2rem 0; height: 1px; background: linear-gradient(to right, #e5e7eb, #d1d5db, #e5e7eb);"></div>
    `
  }

  private static generateDetailsHTML(
    invoice: InvoiceWithRelations,
    company: CompanySettings,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <!-- Modern Client and Invoice Details - matches updated ProfessionalInvoiceLayout -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 3rem;">
            <!-- Bill To - Modern Card Style -->
            <div style="background: #f9fafb; border-radius: 0.5rem; padding: 1.5rem;">
                <div style="font-size: 0.75rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 1rem;">
                    ${showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' : showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
                </div>
                <div style="line-height: 1.5;">
                    <p style="font-weight: 600; color: #111827; font-size: 1.125rem; margin-bottom: 0.5rem;">${invoice.client?.name || ''}</p>
                    ${invoice.client?.company ? `<p style="color: #6b7280; font-weight: 500; margin-bottom: 0.75rem;">${invoice.client.company}</p>` : ''}
                    ${invoice.client?.address && typeof invoice.client.address === 'object' ? `
                        <div style="color: #6b7280; font-size: 0.875rem; line-height: 1.25; margin-bottom: 0.75rem;">
                            <p>${invoice.client.address.street || ''}</p>
                            <p>${invoice.client.address.city || ''}, ${invoice.client.address.state || ''}</p>
                            <p>${invoice.client.address.postal_code || ''}</p>
                        </div>
                    ` : ''}
                    <div style="padding-top: 0.75rem;">
                        ${invoice.client?.email ? `<p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">${invoice.client.email}</p>` : ''}
                        ${invoice.client?.phone ? `<p style="color: #6b7280; font-size: 0.875rem;">${invoice.client.phone}</p>` : ''}
                    </div>
                </div>
            </div>

            <!-- Invoice Details - Clean List Style -->
            <div>
                <div style="font-size: 0.75rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 1rem;">
                    ${showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' : showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
                </div>
                <div style="line-height: 1.5;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                        <span style="color: #6b7280; font-size: 0.875rem;">
                            ${showIndonesian ? 'Tanggal Faktur' : 'Invoice Date'}
                        </span>
                        <span style="font-weight: 500; color: #111827;">${new Date(invoice.created_at).toLocaleDateString('id-ID')}</span>
                    </div>
                    ${invoice.due_date ? `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                            <span style="color: #6b7280; font-size: 0.875rem;">
                                ${showIndonesian ? 'Jatuh Tempo' : 'Due Date'}
                            </span>
                            <span style="font-weight: 500; color: #111827;">${new Date(invoice.due_date).toLocaleDateString('id-ID')}</span>
                        </div>
                    ` : ''}
                    ${invoice.project ? `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                            <span style="color: #6b7280; font-size: 0.875rem;">
                                ${showIndonesian ? 'Proyek' : 'Project'}
                            </span>
                            <span style="font-weight: 500; color: #111827;">${invoice.project.name}</span>
                        </div>
                    ` : ''}
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0;">
                        <span style="color: #6b7280; font-size: 0.875rem;">
                            ${showIndonesian ? 'Mata Uang' : 'Currency'}
                        </span>
                        <span style="font-weight: 500; color: #111827;">${invoice.currency}</span>
                    </div>
                </div>
            </div>
        </div>
    `
  }

  private static generateItemsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    const items = Array.isArray(invoice.items) ? invoice.items : []
    
    return `
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50%;">${showIndonesian && showEnglish ? 'Description / Deskripsi' : showIndonesian ? 'Deskripsi' : 'Description'}</th>
                    <th style="width: 16.67%; text-align: center;">${showIndonesian ? 'Jumlah' : 'Qty'}</th>
                    <th style="width: 16.67%; text-align: center;">${showIndonesian ? 'Harga' : 'Rate'}</th>
                    <th style="width: 16.67%; text-align: right;">${showIndonesian ? 'Total' : 'Amount'}</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((item: any) => `
                    <tr>
                        <td>${item.description || ''}</td>
                        <td style="text-align: center;">${item.quantity || 0}</td>
                        <td style="text-align: center;">${this.formatCurrency(item.rate || 0, invoice.currency)}</td>
                        <td style="text-align: right; font-weight: 600;">${this.formatCurrency(item.amount || 0, invoice.currency)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `
  }

  private static generateTotalsHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td style="padding: 0.5rem 0; color: #6b7280;">${showIndonesian ? 'Subtotal:' : 'Subtotal:'}</td>
                    <td style="padding: 0.5rem 0; text-align: right; font-weight: 600;">${this.formatCurrency(invoice.amount, invoice.currency)}</td>
                </tr>
                ${invoice.tax_amount > 0 ? `
                    <tr>
                        <td style="padding: 0.5rem 0; color: #6b7280;">${showIndonesian ? 'Pajak:' : 'Tax:'}</td>
                        <td style="padding: 0.5rem 0; text-align: right; font-weight: 600;">${this.formatCurrency(invoice.tax_amount, invoice.currency)}</td>
                    </tr>
                ` : ''}
                <tr style="border-top: 2px solid #374151; border-bottom: 3px double #374151;">
                    <td style="padding: 0.75rem 0; font-size: 1.125rem; font-weight: 700; color: #111827;">${showIndonesian ? 'Total:' : 'Total:'}</td>
                    <td style="padding: 0.75rem 0; text-align: right; font-size: 1.125rem; font-weight: 700; color: #111827;">${this.formatCurrency(invoice.total_amount, invoice.currency)}</td>
                </tr>
            </table>
        </div>
    `
  }

  private static generateNotesHTML(
    invoice: InvoiceWithRelations,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    if (!invoice.notes) return ''
    
    return `
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin-bottom: 0.5rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.25rem;">
                ${showIndonesian ? 'Catatan' : 'Notes'}
            </h3>
            <p style="color: #374151; line-height: 1.6;">${invoice.notes}</p>
        </div>
    `
  }

  private static generateFooterHTML(
    company: CompanySettings,
    primaryBankAccount: any,
    template: InvoiceTemplate | null | undefined,
    showIndonesian: boolean,
    showEnglish: boolean
  ): string {
    return `
        <!-- Modern Simplified Footer - matches updated ProfessionalInvoiceLayout -->
        <div style="margin-top: 4rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
            <!-- Essential Payment Information Only -->
            ${template?.content?.show_bank_details && primaryBankAccount ? `
                <div style="background: #f9fafb; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 1.5rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 0.75rem; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                            ${showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}
                        </div>
                        <div style="line-height: 1.5;">
                            <p style="font-weight: 600; color: #111827; font-size: 1.125rem; margin-bottom: 0.5rem;">${primaryBankAccount.bank_name}</p>
                            <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; font-size: 0.875rem;">
                                <div style="text-align: center;">
                                    <p style="color: #6b7280; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em;">
                                        ${showIndonesian ? 'Nama Rekening' : 'Account Name'}
                                    </p>
                                    <p style="font-weight: 500; color: #111827;">${primaryBankAccount.account_name}</p>
                                </div>
                                <div style="text-align: center;">
                                    <p style="color: #6b7280; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em;">
                                        ${showIndonesian ? 'Nomor Rekening' : 'Account Number'}
                                    </p>
                                    <p style="font-family: monospace; font-weight: bold; color: #111827; font-size: 1.125rem;">${primaryBankAccount.account_number}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ` : ''}

            <!-- Minimal Company Footer -->
            <div style="text-align: center; line-height: 1.5;">
                <p style="font-weight: 500; color: #111827; margin-bottom: 0.5rem;">${company.name}</p>
                <div style="display: flex; justify-content: center; align-items: center; gap: 1rem; font-size: 0.875rem; color: #6b7280;">
                    <span>${company.email}</span>
                    <span>•</span>
                    <span>${company.phone}</span>
                    ${company.website ? `
                        <span>•</span>
                        <span>${company.website}</span>
                    ` : ''}
                </div>
                ${template?.content?.show_tax_id && company.tax_id ? `
                    <p style="font-size: 0.75rem; color: #9ca3af; margin-top: 0.5rem;">NPWP: ${company.tax_id}</p>
                ` : ''}
            </div>

            <!-- Footer Text -->
            ${company.invoice_settings?.invoice_footer_text ? `
                <div style="text-align: center; margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #f3f4f6;">
                    <p style="color: #9ca3af; font-size: 0.75rem;">
                        ${company.invoice_settings.invoice_footer_text}
                    </p>
                </div>
            ` : ''}
        </div>
    `
  }

  private static formatCurrency(amount: number, currency: string): string {
    if (currency === 'IDR') {
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }
}
