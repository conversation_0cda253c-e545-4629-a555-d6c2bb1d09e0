import { createClient } from './client'

export async function testSupabaseConnection() {
  try {
    const supabase = createClient()
    
    // Test the connection by trying to get the current user
    const { data, error } = await supabase.auth.getUser()
    
    if (error && error.message !== 'Auth session missing!') {
      console.error('Supabase connection error:', error)
      return false
    }
    
    console.log('Supabase connection successful!')
    return true
  } catch (error) {
    console.error('Failed to connect to Supabase:', error)
    return false
  }
}
