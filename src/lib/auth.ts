import { createClient } from '@/lib/supabase/client'

export async function getCurrentUser() {
  const supabase = createClient()
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      console.error('Error getting current user:', error)
      return null
    }
    
    return user
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

export async function getCurrentUserId(): Promise<string | null> {
  const user = await getCurrentUser()
  return user?.id || null
}

// Fallback UUID for development/testing when no user is authenticated
export const FALLBACK_USER_ID = "00000000-0000-0000-0000-000000000000"

export async function getCurrentUserIdOrFallback(): Promise<string> {
  const userId = await getCurrentUserId()
  return userId || FALLBACK_USER_ID
}
