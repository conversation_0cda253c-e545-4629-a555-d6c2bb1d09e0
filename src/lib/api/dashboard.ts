import { createClient as createServerClient } from '@/lib/supabase/server'
import { DashboardMetrics, ChartData } from '@/lib/types'

export async function getRevenueChartData(): Promise<ChartData[]> {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select('total_amount, paid_date')
    .eq('status', 'paid')
    .not('paid_date', 'is', null)
    .gte('paid_date', new Date(new Date().getFullYear(), 0, 1).toISOString())
    .order('paid_date', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch revenue chart data: ${error.message}`)
  }
  
  // Group by month
  const monthlyRevenue: { [key: string]: number } = {}
  
  data.forEach((invoice) => {
    const month = new Date(invoice.paid_date!).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short' 
    })
    monthlyRevenue[month] = (monthlyRevenue[month] || 0) + Number(invoice.total_amount)
  })
  
  return Object.entries(monthlyRevenue).map(([month, value]) => ({
    name: month,
    value,
    date: month
  }))
}

export async function getProjectStatusData(): Promise<ChartData[]> {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select('status')
  
  if (error) {
    throw new Error(`Failed to fetch project status data: ${error.message}`)
  }
  
  const statusCounts: { [key: string]: number } = {}
  
  data.forEach((project) => {
    statusCounts[project.status] = (statusCounts[project.status] || 0) + 1
  })
  
  return Object.entries(statusCounts).map(([name, value]) => ({
    name: name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    value
  }))
}

export async function getRecentActivities() {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('activities')
    .select(`
      *,
      user:users_profiles(id, full_name)
    `)
    .order('created_at', { ascending: false })
    .limit(10)
  
  if (error) {
    throw new Error(`Failed to fetch recent activities: ${error.message}`)
  }
  
  return data
}

// Server-side functions
export async function getDashboardMetricsServer(): Promise<DashboardMetrics> {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase.rpc('get_dashboard_metrics')
  
  if (error) {
    throw new Error(`Failed to fetch dashboard metrics: ${error.message}`)
  }
  
  return data as DashboardMetrics
}
