"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"

export async function signOut() {
  const supabase = await createClient()
  
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    console.error("Error signing out:", error)
    return
  }
  
  revalidatePath("/", "layout")
  redirect("/login")
}

export async function getCurrentUser() {
  const supabase = await createClient()
  
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser()
  
  if (error || !user) {
    return null
  }
  
  return user
}

export async function getUserProfile() {
  const supabase = await createClient()
  const user = await getCurrentUser()
  
  if (!user) {
    return null
  }
  
  const { data: profile, error } = await supabase
    .from("users_profiles")
    .select("*")
    .eq("id", user.id)
    .single()
  
  if (error) {
    console.error("Error fetching user profile:", error)
    return null
  }
  
  return profile
}
