/**
 * Test file for Payment Milestone System
 * This file contains basic tests to verify the milestone system functionality
 */

import { validateMilestonePercentages } from '@/lib/utils/budget-validation'

describe('Payment Milestone System', () => {
  describe('validateMilestonePercentages', () => {
    test('should validate correct milestone percentages', () => {
      const milestones = [
        { percentage: 50, type: 'dp' },
        { percentage: 50, type: 'final' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.isValid).toBe(true)
      expect(result.totalPercentage).toBe(100)
      expect(result.totalAmount).toBe(8000000)
      expect(result.errors).toHaveLength(0)
    })

    test('should reject incorrect total percentage', () => {
      const milestones = [
        { percentage: 40, type: 'dp' },
        { percentage: 50, type: 'final' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.isValid).toBe(false)
      expect(result.totalPercentage).toBe(90)
      expect(result.errors).toContain('Total milestone percentages must equal 100%. Current total: 90%')
    })

    test('should reject duplicate milestone types', () => {
      const milestones = [
        { percentage: 50, type: 'dp' },
        { percentage: 50, type: 'dp' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Only one dp milestone is allowed')
    })

    test('should allow multiple progress milestones', () => {
      const milestones = [
        { percentage: 30, type: 'dp' },
        { percentage: 30, type: 'progress' },
        { percentage: 20, type: 'progress' },
        { percentage: 20, type: 'final' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.isValid).toBe(true)
      expect(result.totalPercentage).toBe(100)
    })

    test('should warn about low down payment', () => {
      const milestones = [
        { percentage: 10, type: 'dp' },
        { percentage: 90, type: 'final' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.warnings).toContain('Down payment is less than 20% - consider increasing for better cash flow')
    })

    test('should warn about high final payment', () => {
      const milestones = [
        { percentage: 20, type: 'dp' },
        { percentage: 80, type: 'final' }
      ]
      const projectBudget = 8000000
      
      const result = validateMilestonePercentages(milestones, projectBudget)
      
      expect(result.warnings).toContain('Final payment is more than 50% - consider breaking into progress payments')
    })
  })
})

// Mock test data for Indonesian business scenarios
export const mockIndonesianProjectScenarios = {
  // Typical web development project
  webDevelopment: {
    budget: 15000000, // 15 million IDR
    milestones: [
      { type: 'dp', percentage: 40, description: 'Down Payment - Project Initiation' },
      { type: 'progress', percentage: 30, description: 'Progress Payment - Development Phase' },
      { type: 'final', percentage: 30, description: 'Final Payment - Project Completion' }
    ]
  },
  
  // Large enterprise project
  enterpriseProject: {
    budget: 50000000, // 50 million IDR
    milestones: [
      { type: 'dp', percentage: 30, description: 'Down Payment - Project Kickoff' },
      { type: 'progress', percentage: 25, description: 'Progress Payment - Phase 1 Completion' },
      { type: 'progress', percentage: 25, description: 'Progress Payment - Phase 2 Completion' },
      { type: 'final', percentage: 20, description: 'Final Payment - Project Delivery' }
    ]
  },
  
  // Small consulting project
  consultingProject: {
    budget: 5000000, // 5 million IDR
    milestones: [
      { type: 'dp', percentage: 50, description: 'Down Payment - Consulting Agreement' },
      { type: 'final', percentage: 50, description: 'Final Payment - Report Delivery' }
    ]
  }
}

// Test Indonesian currency formatting
describe('Indonesian Currency Support', () => {
  test('should format IDR currency correctly', () => {
    const amount = 8000000
    const formatted = new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
    
    expect(formatted).toContain('8.000.000')
    expect(formatted).toContain('Rp')
  })
})
