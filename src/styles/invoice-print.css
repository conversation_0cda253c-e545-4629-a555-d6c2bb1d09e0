/* Professional Invoice Print Styles */
/* Optimized for PDF generation and high-quality printing */

.print-layout {
  /* Page setup for A4 size */
  width: 210mm;
  min-height: 297mm;
  margin: 0;
  padding: 20mm;
  background: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #1f2937;
}

/* Print media queries */
@media print {
  .print-layout {
    margin: 0;
    padding: 15mm;
    box-shadow: none;
    border: none;
  }
  
  /* Hide non-essential elements when printing */
  .no-print {
    display: none !important;
  }
  
  /* Ensure proper page breaks */
  .page-break-before {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .page-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  /* Optimize colors for print */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}

/* Invoice Header Styles */
.invoice-header {
  margin-bottom: 2rem;
  page-break-inside: avoid;
}

.company-logo img {
  max-height: 80px;
  max-width: 200px;
  object-fit: contain;
}

.company-info h1 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
}

.company-info p {
  margin: 0;
  line-height: 1.5;
}

/* Invoice Title Styling */
.invoice-title {
  font-size: 2.25rem;
  font-weight: 800;
  color: #111827;
  letter-spacing: -0.025em;
}

.invoice-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

/* Client Information */
.client-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.client-details p {
  margin: 0.25rem 0;
  color: #374151;
}

.client-details .client-name {
  font-weight: 600;
  color: #111827;
  font-size: 1.1em;
}

/* Invoice Details Grid */
.invoice-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  page-break-inside: avoid;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.detail-label {
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-weight: 600;
  color: #111827;
}

/* Items Table */
.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2rem;
  page-break-inside: avoid;
}

.items-table th {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
}

.items-table td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  color: #374151;
}

.items-table .description-col {
  width: 50%;
}

.items-table .quantity-col,
.items-table .rate-col,
.items-table .amount-col {
  width: 16.67%;
  text-align: right;
}

.items-table .quantity-col {
  text-align: center;
}

/* Totals Section */
.totals-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2rem;
}

.totals-table {
  width: 300px;
  border-collapse: collapse;
}

.totals-table td {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.totals-table .label {
  color: #6b7280;
  font-weight: 500;
  text-align: left;
}

.totals-table .amount {
  font-weight: 600;
  color: #111827;
  text-align: right;
}

.totals-table .total-row {
  border-top: 2px solid #374151;
  border-bottom: 3px double #374151;
  font-size: 1.125rem;
  font-weight: 700;
}

.totals-table .total-row td {
  padding: 0.75rem 0;
  color: #111827;
}

/* Payment Information */
.payment-info {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  page-break-inside: avoid;
}

.payment-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.bank-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.bank-detail-item {
  margin-bottom: 0.5rem;
}

.bank-name {
  font-weight: 600;
  color: #111827;
  font-size: 1.1em;
}

.bank-label {
  color: #6b7280;
  font-weight: 500;
}

.bank-value {
  color: #374151;
  font-weight: 600;
}

/* Terms and Notes */
.terms-section,
.notes-section {
  margin-bottom: 1.5rem;
  page-break-inside: avoid;
}

.terms-section h3,
.notes-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.terms-text,
.notes-text {
  color: #374151;
  line-height: 1.6;
}

/* Footer */
.invoice-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: auto;
}

/* Milestone Badge Styling */
.milestone-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Status Indicators */
.status-paid {
  color: #059669;
  background-color: #d1fae5;
  border-color: #6ee7b7;
}

.status-sent {
  color: #0369a1;
  background-color: #dbeafe;
  border-color: #93c5fd;
}

.status-draft {
  color: #6b7280;
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.status-overdue {
  color: #dc2626;
  background-color: #fee2e2;
  border-color: #fca5a5;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .print-layout {
    padding: 1rem;
  }
  
  .invoice-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .bank-details {
    grid-template-columns: 1fr;
  }
  
  .items-table {
    font-size: 0.75rem;
  }
  
  .items-table th,
  .items-table td {
    padding: 0.5rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .print-layout {
    color: #000000;
  }
  
  .items-table th {
    background-color: #000000;
    color: #ffffff;
  }
  
  .payment-info {
    background-color: #f0f0f0;
    border-color: #000000;
  }
}
